<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Job extends Model
{
    use HasFactory;

    protected $primaryKey = 'job_description_id';
    
    protected $fillable = [
        'job_description',
        'highlight',
    ];

    protected $casts = [
        'highlight' => 'boolean',
    ];

    /**
     * Get the daily reports that use this job.
     */
    public function dailyReports()
    {
        return $this->belongsToMany(DailyReport::class, 'daily_report_jobs', 'job_description_id', 'daily_report_id');
    }
}
