@extends('sites.content')

@section('title', 'Daily Reports')

@section('resourcesite')
@vite(['resources/js/daily-reports.js'])
@endsection

@section('contentsite')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('sites.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Daily Reports</li>
                    </ol>
                </div>
                <h4 class="page-title">Daily Reports</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Data Table Section (Left Side) -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h4 class="header-title">Daftar Daily Reports</h4>
                            <p class="text-muted">Kelola data laporan harian unit</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <button type="button" class="btn btn-primary" id="add-daily-report-btn">
                                <i class="mdi mdi-plus-circle me-1"></i> Tambah Daily Report
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="search-input" placeholder="Cari...">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="start-date" placeholder="Tanggal Mulai">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="end-date" placeholder="Tanggal Akhir">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="shift-filter">
                                <option value="">Semua Shift</option>
                                <option value="DAY">DAY</option>
                                <option value="NIGHT">NIGHT</option>
                            </select>
                        </div>
                    </div>

                    <!-- Loading Skeleton -->
                    <div id="loading-skeleton" class="d-none">
                        <div class="skeleton-loader">
                            <div class="skeleton-row"></div>
                            <div class="skeleton-row"></div>
                            <div class="skeleton-row"></div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="daily-reports-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>Unit Code</th>
                                    <th>Tanggal</th>
                                    <th>Shift</th>
                                    <th>Problem</th>
                                    <th>Teknisi</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div id="pagination-container" class="d-flex justify-content-center mt-3">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Section (Right Side) -->
        <div class="col-lg-4">
            <div class="card" id="form-card" style="display: none;">
                <div class="card-body">
                    <h4 class="header-title" id="form-title">Tambah Daily Report</h4>
                    
                    <form id="daily-report-form" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" id="daily-report-id" name="id">
                        
                        <!-- Unit Auto-Search -->
                        <div class="mb-3">
                            <label for="unit_search" class="form-label">Unit <span class="text-danger">*</span></label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="unit_search" placeholder="Cari unit berdasarkan kode atau tipe..." autocomplete="off">
                                <input type="hidden" id="unit_id" name="unit_id" required>
                                <div id="unit_dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                    <!-- Search results will appear here -->
                                </div>
                            </div>
                            <small class="text-muted">Ketik minimal 2 karakter untuk mencari unit</small>
                        </div>

                        <!-- HM -->
                        <div class="mb-3">
                            <label for="hm" class="form-label">HM</label>
                            <input type="number" class="form-control" id="hm" name="hm" step="0.01" min="0">
                        </div>

                        <!-- Problem Fields -->
                        <div class="mb-3">
                            <label for="problem" class="form-label">Problem</label>
                            <input type="text" class="form-control" id="problem" name="problem">
                        </div>

                        <div class="mb-3">
                            <label for="problem_component" class="form-label">Problem Component</label>
                            <input type="text" class="form-control" id="problem_component" name="problem_component">
                        </div>

                        <div class="mb-3">
                            <label for="problem_description" class="form-label">Problem Description</label>
                            <textarea class="form-control" id="problem_description" name="problem_description" rows="3"></textarea>
                        </div>

                        <!-- Date and Time -->
                        <div class="mb-3">
                            <label for="date_in" class="form-label">Tanggal <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date_in" name="date_in" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hour_in" class="form-label">Jam Masuk <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="hour_in" name="hour_in" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hour_out" class="form-label">Jam Keluar <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="hour_out" name="hour_out" required>
                                </div>
                            </div>
                        </div>

                        <!-- Shift -->
                        <div class="mb-3">
                            <label for="shift" class="form-label">Shift <span class="text-danger">*</span></label>
                            <select class="form-control" id="shift" name="shift" required>
                                <option value="">Pilih Shift</option>
                                <option value="DAY">DAY</option>
                                <option value="NIGHT">NIGHT</option>
                            </select>
                        </div>

                        <!-- Jobs Management -->
                        <div class="mb-3">
                            <label class="form-label">Pekerjaan <span class="text-danger">*</span></label>

                            <!-- Inline Job Creation Form -->
                            <div class="border rounded p-3 mb-3 bg-light">
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" id="job_description_input" placeholder="Deskripsi pekerjaan...">
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="job_highlight_input">
                                            <label class="form-check-label" for="job_highlight_input">
                                                <i class="mdi mdi-star text-warning"></i> Highlight
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-primary btn-sm w-100" id="add_job_btn">
                                            <i class="mdi mdi-plus"></i> Tambah
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Jobs List -->
                            <div id="jobs_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                <small class="text-muted">Belum ada pekerjaan ditambahkan</small>
                            </div>
                            <small class="text-muted">Minimal satu pekerjaan harus ditambahkan</small>
                        </div>

                        <!-- Technicians Management -->
                        <div class="mb-3">
                            <label class="form-label">Teknisi <span class="text-danger">*</span></label>

                            <!-- Inline Technician Creation Form -->
                            <div class="border rounded p-3 mb-3 bg-light">
                                <div class="row">
                                    <div class="col-md-10">
                                        <input type="text" class="form-control" id="technician_name_input" placeholder="Nama teknisi...">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-primary btn-sm w-100" id="add_technician_btn">
                                            <i class="mdi mdi-plus"></i> Tambah
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Technicians List -->
                            <div id="technicians_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                <small class="text-muted">Belum ada teknisi ditambahkan</small>
                            </div>
                            <small class="text-muted">Minimal satu teknisi harus ditambahkan</small>
                        </div>

                        <!-- Image Uploads -->
                        <div class="mb-3">
                            <label for="before_images" class="form-label">Gambar Sebelum</label>
                            <input type="file" class="form-control" id="before_images" name="before_images[]" multiple accept="image/*">
                            <small class="text-muted">Maksimal 5MB per gambar. Format: JPEG, PNG, JPG, GIF</small>
                            <div id="before-images-preview" class="mt-2"></div>
                        </div>

                        <div class="mb-3">
                            <label for="after_images" class="form-label">Gambar Sesudah</label>
                            <input type="file" class="form-control" id="after_images" name="after_images[]" multiple accept="image/*">
                            <small class="text-muted">Maksimal 5MB per gambar. Format: JPEG, PNG, JPG, GIF</small>
                            <div id="after-images-preview" class="mt-2"></div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" id="cancel-btn">Batal</button>
                            <button type="submit" class="btn btn-primary" id="save-btn">
                                <i class="mdi mdi-content-save"></i> Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- View Daily Report Modal -->
<div class="modal fade" id="view-daily-report-modal" tabindex="-1" aria-labelledby="view-daily-report-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="view-daily-report-modal-label">Detail Daily Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="daily-report-details">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<style>
.skeleton-loader {
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-row {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.image-preview {
    position: relative;
    display: inline-block;
    margin: 5px;
}

.image-preview img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.image-preview .remove-image {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
}

/* Unit dropdown styles */
#unit_dropdown {
    z-index: 1050;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#unit_dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

#unit_dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
}

#unit_dropdown .dropdown-item:last-child {
    border-bottom: none;
}

/* Job and technician list item styles */
.job-item, .technician-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.job-item.highlighted {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.job-item .job-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.job-item .highlight-badge {
    color: #ffc107;
    font-weight: bold;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-btn:hover {
    background: #c82333;
}
</style>
@endsection
