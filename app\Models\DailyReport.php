<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DailyReport extends Model
{
    use HasFactory;

    protected $primaryKey = 'daily_report_id';
    
    protected $fillable = [
        'unit_id',
        'hm',
        'problem',
        'problem_component',
        'problem_description',
        'date_in',
        'hour_in',
        'hour_out',
        'shift',
    ];

    protected $casts = [
        'date_in' => 'date',
        'hour_in' => 'datetime:H:i',
        'hour_out' => 'datetime:H:i',
        'hm' => 'float',
    ];

    /**
     * Get the unit that owns the daily report.
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_id', 'id');
    }



    /**
     * Get the jobs for the daily report.
     */
    public function jobs()
    {
        return $this->belongsToMany(Job::class, 'daily_report_jobs', 'daily_report_id', 'job_description_id');
    }

    /**
     * Get the technicians for the daily report.
     */
    public function technicians()
    {
        return $this->belongsToMany(Technician::class, 'daily_report_technicians', 'daily_report_id', 'technician_id');
    }

    /**
     * Get the images for the daily report.
     */
    public function images()
    {
        return $this->hasMany(DailyReportImage::class, 'daily_report_id', 'daily_report_id');
    }

    /**
     * Get before images.
     */
    public function beforeImages()
    {
        return $this->hasMany(DailyReportImage::class, 'daily_report_id', 'daily_report_id')
                    ->where('type', 'before');
    }

    /**
     * Get after images.
     */
    public function afterImages()
    {
        return $this->hasMany(DailyReportImage::class, 'daily_report_id', 'daily_report_id')
                    ->where('type', 'after');
    }
}
