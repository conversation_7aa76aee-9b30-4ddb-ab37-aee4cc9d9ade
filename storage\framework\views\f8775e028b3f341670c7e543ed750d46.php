<?php $__env->startSection('title', 'Monthly Report Sales'); ?>
<?php $__env->startSection('resourcesite'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/sites/jasa_karyawan_sales.js']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('contentsite'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('sites.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Monthly Report Sales</li>
                    </ol>
                </div>
                <h4 class="page-title">Monthly Report Sales</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <button type="button" class="btn btn-primary" id="btn-add-jasa-karyawan">
                                <i class="mdi mdi-plus-circle me-1"></i> Tambah Monthly Report Sales
                            </button>
                        </div>
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" class="form-control" id="search-input" placeholder="Cari...">
                                <button class="btn btn-primary" type="button" id="btn-search">
                                    <i class="mdi mdi-magnify"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="status-filter" class="form-label">Status</label>
                            <select class="form-select" id="status-filter">
                                <option value="">Semua Status</option>
                                <option value="submitted">Submitted</option>
                                <option value="approved">Approved</option>
                                <option value="rejected">Rejected</option>
                                <option value="done">Done</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="start-date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start-date">
                        </div>
                        <div class="col-md-3">
                            <label for="end-date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end-date">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" class="btn btn-primary w-100" id="btn-filter">
                                <i class="mdi mdi-filter me-1"></i> Filter
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-centered table-hover mb-0" id="jasa-karyawan-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tanggal</th>
                                    <th>Status</th>
                                    <th>File</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="pagination-container d-flex justify-content-end">
                                <!-- Pagination will be loaded here via AJAX -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Monthly Report Modal -->
<div class="modal fade" id="jasa-karyawan-modal" tabindex="-1" aria-labelledby="jasa-karyawan-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="jasa-karyawan-modal-label">Tambah Monthly Report Sales</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="jasa-karyawan-form" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" id="jasa-karyawan-id" name="id">
                    <input type="hidden" id="type" name="type" value="sales">

                    <div class="mb-3">
                        <label for="date" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="date" name="date" required>
                    </div>

                    <input type="hidden" id="amount" name="amount" value="0">
                    <input type="hidden" id="notes" name="notes" value="">

                    <div class="mb-3">
                        <label for="file" class="form-label">File (Maks. 10MB)</label>
                        <div class="custom-file">
                            <input type="file" class="form-control" id="file" name="file">
                            <small class="text-muted d-block mt-1">Format yang diizinkan: PDF, JPG, JPEG, PNG, DOC, DOCX, XLS, XLSX</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="btn-save-jasa-karyawan">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- View File Modal -->
<div class="modal fade" id="view-file-modal" tabindex="-1" aria-labelledby="view-file-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="view-file-modal-label">Lihat File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="file-container" class="text-center">
                    <!-- File will be displayed here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a href="#" class="btn btn-primary" id="download-file" target="_blank">Download</a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/sites/jasa_karyawan/index_sales.blade.php ENDPATH**/ ?>