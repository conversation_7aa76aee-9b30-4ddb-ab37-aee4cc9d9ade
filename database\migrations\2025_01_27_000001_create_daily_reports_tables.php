<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create daily_reports table
        Schema::create('daily_reports', function (Blueprint $table) {
            $table->id('daily_report_id');
            $table->foreignId('unit_id')->constrained('units')->onUpdate('cascade');
            $table->string('code_part', 50)->nullable()->default(null);
            $table->foreign('code_part')
                ->references('part_code')
                ->on('parts')
                ->onUpdate('cascade')
                ->onDelete('set null');
            $table->float('hm')->nullable();
            $table->string('problem')->nullable();
            $table->string('problem_component')->nullable();
            $table->text('problem_description')->nullable();
            $table->date('date_in');
            $table->time('hour_in');
            $table->time('hour_out');
            $table->enum('shift', ['DAY', 'NIGHT']);
            $table->timestamps();
        });

        // Create daily_report_job_descriptions table
        Schema::create('daily_report_job_descriptions', function (Blueprint $table) {
            $table->id('job_description_id');
            $table->string('job_description');
            $table->boolean('highlight')->default(false);
            $table->timestamps();
        });

        // Create technicians table
        Schema::create('technicians', function (Blueprint $table) {
            $table->id('technician_id');
            $table->string('name');
            $table->timestamps();
        });

        // Create daily_report_jobs pivot table
        Schema::create('daily_report_jobs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('daily_report_id')->constrained('daily_reports', 'daily_report_id')->onDelete('cascade');
            $table->foreignId('job_description_id')->constrained('daily_report_job_descriptions', 'job_description_id')->onDelete('cascade');
            $table->timestamps();
        });

        // Create daily_report_technicians pivot table
        Schema::create('daily_report_technicians', function (Blueprint $table) {
            $table->id();
            $table->foreignId('daily_report_id')->constrained('daily_reports', 'daily_report_id')->onDelete('cascade');
            $table->foreignId('technician_id')->constrained('technicians', 'technician_id')->onDelete('cascade');
            $table->timestamps();
        });

        // Create daily_report_images table
        Schema::create('daily_report_images', function (Blueprint $table) {
            $table->id();
            $table->foreignId('daily_report_id')->constrained('daily_reports', 'daily_report_id')->onDelete('cascade');
            $table->string('image_path');
            $table->enum('type', ['before', 'after']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_report_images');
        Schema::dropIfExists('daily_report_technicians');
        Schema::dropIfExists('daily_report_jobs');
        Schema::dropIfExists('technicians');
        Schema::dropIfExists('daily_report_job_descriptions');
        Schema::dropIfExists('daily_reports');
    }
};
