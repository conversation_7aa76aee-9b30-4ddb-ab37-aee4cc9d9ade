<?php $__env->startSection('title', 'Daily Reports'); ?>

<?php $__env->startSection('resourcesite'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/daily-reports.js']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('contentsite'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('sites.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Daily Reports</li>
                    </ol>
                </div>
                <h4 class="page-title">Daily Reports</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Data Table Section (Left Side) -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h4 class="header-title">Daftar Daily Reports</h4>
                            <p class="text-muted">Kelola data laporan harian unit</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <button type="button" class="btn btn-primary" id="add-daily-report-btn">
                                <i class="mdi mdi-plus-circle me-1"></i> Tambah Daily Report
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="search-input" placeholder="Cari...">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="start-date" placeholder="Tanggal Mulai">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="end-date" placeholder="Tanggal Akhir">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="shift-filter">
                                <option value="">Semua Shift</option>
                                <option value="DAY">DAY</option>
                                <option value="NIGHT">NIGHT</option>
                            </select>
                        </div>
                    </div>

                    <!-- Loading Skeleton -->
                    <div id="loading-skeleton" class="d-none">
                        <div class="skeleton-loader">
                            <div class="skeleton-row"></div>
                            <div class="skeleton-row"></div>
                            <div class="skeleton-row"></div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="daily-reports-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>Unit Code</th>
                                    <th>Tanggal</th>
                                    <th>Shift</th>
                                    <th>Problem</th>
                                    <th>Teknisi</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div id="pagination-container" class="d-flex justify-content-center mt-3">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Section (Right Side) -->
        <div class="col-lg-4">
            <div class="card" id="form-card" style="display: none;">
                <div class="card-body">
                    <h4 class="header-title" id="form-title">Tambah Daily Report</h4>
                    
                    <form id="daily-report-form" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" id="daily-report-id" name="id">
                        
                        <!-- Unit Selection -->
                        <div class="mb-3">
                            <label for="unit_id" class="form-label">Unit <span class="text-danger">*</span></label>
                            <select class="form-control" id="unit_id" name="unit_id" required>
                                <option value="">Pilih Unit</option>
                                <?php $__currentLoopData = $units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($unit->id); ?>"><?php echo e($unit->unit_code); ?> - <?php echo e($unit->unit_type); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Part Selection (Optional) -->
                        <div class="mb-3">
                            <label for="code_part" class="form-label">Part</label>
                            <select class="form-control" id="code_part" name="code_part">
                                <option value="">Pilih Part (Opsional)</option>
                                <?php $__currentLoopData = $parts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $part): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($part->part_code); ?>"><?php echo e($part->part_code); ?> - <?php echo e($part->part_name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- HM -->
                        <div class="mb-3">
                            <label for="hm" class="form-label">HM</label>
                            <input type="number" class="form-control" id="hm" name="hm" step="0.01" min="0">
                        </div>

                        <!-- Problem Fields -->
                        <div class="mb-3">
                            <label for="problem" class="form-label">Problem</label>
                            <input type="text" class="form-control" id="problem" name="problem">
                        </div>

                        <div class="mb-3">
                            <label for="problem_component" class="form-label">Problem Component</label>
                            <input type="text" class="form-control" id="problem_component" name="problem_component">
                        </div>

                        <div class="mb-3">
                            <label for="problem_description" class="form-label">Problem Description</label>
                            <textarea class="form-control" id="problem_description" name="problem_description" rows="3"></textarea>
                        </div>

                        <!-- Date and Time -->
                        <div class="mb-3">
                            <label for="date_in" class="form-label">Tanggal <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date_in" name="date_in" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hour_in" class="form-label">Jam Masuk <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="hour_in" name="hour_in" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hour_out" class="form-label">Jam Keluar <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="hour_out" name="hour_out" required>
                                </div>
                            </div>
                        </div>

                        <!-- Shift -->
                        <div class="mb-3">
                            <label for="shift" class="form-label">Shift <span class="text-danger">*</span></label>
                            <select class="form-control" id="shift" name="shift" required>
                                <option value="">Pilih Shift</option>
                                <option value="DAY">DAY</option>
                                <option value="NIGHT">NIGHT</option>
                            </select>
                        </div>

                        <!-- Jobs Selection -->
                        <div class="mb-3">
                            <label class="form-label">Pekerjaan <span class="text-danger">*</span></label>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">Pilih minimal satu pekerjaan</small>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="add-job-btn">
                                    <i class="mdi mdi-plus"></i> Tambah Pekerjaan
                                </button>
                            </div>
                            <div id="jobs-container">
                                <?php $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="jobs[]" value="<?php echo e($job->job_description_id); ?>" id="job_<?php echo e($job->job_description_id); ?>">
                                        <label class="form-check-label <?php echo e($job->highlight ? 'fw-bold text-warning' : ''); ?>" for="job_<?php echo e($job->job_description_id); ?>">
                                            <?php echo e($job->job_description); ?>

                                            <?php if($job->highlight): ?>
                                                <i class="mdi mdi-star text-warning"></i>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- Technicians Selection -->
                        <div class="mb-3">
                            <label class="form-label">Teknisi <span class="text-danger">*</span></label>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">Pilih minimal satu teknisi</small>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="add-technician-btn">
                                    <i class="mdi mdi-plus"></i> Tambah Teknisi
                                </button>
                            </div>
                            <div id="technicians-container">
                                <?php $__currentLoopData = $technicians; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $technician): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="technicians[]" value="<?php echo e($technician->technician_id); ?>" id="technician_<?php echo e($technician->technician_id); ?>">
                                        <label class="form-check-label" for="technician_<?php echo e($technician->technician_id); ?>">
                                            <?php echo e($technician->name); ?>

                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- Image Uploads -->
                        <div class="mb-3">
                            <label for="before_images" class="form-label">Gambar Sebelum</label>
                            <input type="file" class="form-control" id="before_images" name="before_images[]" multiple accept="image/*">
                            <small class="text-muted">Maksimal 5MB per gambar. Format: JPEG, PNG, JPG, GIF</small>
                            <div id="before-images-preview" class="mt-2"></div>
                        </div>

                        <div class="mb-3">
                            <label for="after_images" class="form-label">Gambar Sesudah</label>
                            <input type="file" class="form-control" id="after_images" name="after_images[]" multiple accept="image/*">
                            <small class="text-muted">Maksimal 5MB per gambar. Format: JPEG, PNG, JPG, GIF</small>
                            <div id="after-images-preview" class="mt-2"></div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" id="cancel-btn">Batal</button>
                            <button type="submit" class="btn btn-primary" id="save-btn">
                                <i class="mdi mdi-content-save"></i> Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Job Modal -->
<div class="modal fade" id="add-job-modal" tabindex="-1" aria-labelledby="add-job-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="add-job-modal-label">Tambah Pekerjaan Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-job-form">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3">
                        <label for="job_description" class="form-label">Deskripsi Pekerjaan <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="job_description" name="job_description" required>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="highlight" name="highlight">
                            <label class="form-check-label" for="highlight">
                                Highlight (Pekerjaan Penting)
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="save-job-btn">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Technician Modal -->
<div class="modal fade" id="add-technician-modal" tabindex="-1" aria-labelledby="add-technician-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="add-technician-modal-label">Tambah Teknisi Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-technician-form">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3">
                        <label for="technician_name" class="form-label">Nama Teknisi <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="technician_name" name="name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="save-technician-btn">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- View Daily Report Modal -->
<div class="modal fade" id="view-daily-report-modal" tabindex="-1" aria-labelledby="view-daily-report-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="view-daily-report-modal-label">Detail Daily Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="daily-report-details">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<style>
.skeleton-loader {
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-row {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.image-preview {
    position: relative;
    display: inline-block;
    margin: 5px;
}

.image-preview img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.image-preview .remove-image {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/daily-reports/index.blade.php ENDPATH**/ ?>